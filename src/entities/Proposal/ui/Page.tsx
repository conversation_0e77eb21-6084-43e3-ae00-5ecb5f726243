import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ChatSquareText, Check2, ChevronLeft } from 'react-bootstrap-icons';
import { Session } from 'next-auth';
import {
  <PERSON><PERSON>,
  Button,
  Container,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';

import { ContentPage } from '~/app/templates/ContentPage';
import { Avatar, TargetCard } from '~/shared/components';
import {
  CommentsModal,
  CommentsModalRefApi,
} from '~/shared/components/CommentsModal';
import { InProgress } from '~/shared/components/icons/InProgress';
import { NotStarted } from '~/shared/components/icons/NotStarted';
import { Proposal } from '~/shared/types/Proposal';
import { ProposalStatusEnum, UserPermissionsEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { ComparativeStep } from '../components/Comparative';
import { FeedbackStep } from '../components/Feedback/FeedbackStep';
import { FinalStep } from '../components/Final/FinalStep';
import { ProposalStep } from '../components/Proposal';
import { TabEmptyState } from '../components/TabEmptyState';
import { PROPOSAL_STATUS } from '../constants';
import { PROPOSAL_STEPS, ProposalStepType } from '../constants/steps';
import { getCurrentProposalStep } from '../utils/getCurrentProposalStep';

interface PageProps {
  proposal: Proposal | undefined;
  onProposalUpdate?: (updatedProposal: Proposal) => void;
  // session: Session | null;
}

export function Page({ proposal, onProposalUpdate }: PageProps) {
  // const userRoles = session?.user?.roles || [];

  // const hasManagerPermission = userRoles.includes(
  //   UserPermissionsEnum.CATCHBALL_MANAGER,
  // );
  // const hasEmployeePermission = userRoles.includes(
  //   UserPermissionsEnum.CATCHBALL_EMPLOYEE,
  // );
  const commentsModalRef = useRef<CommentsModalRefApi>(null);

  // State to determine which tab show to the user
  const [step, setStep] = useState<ProposalStepType>(PROPOSAL_STEPS.PROPOSAL);

  useEffect(() => {
    setStep(getCurrentProposalStep(proposal?.status));
  }, [proposal?.status]);

  const [currentProposal, setCurrentProposal] = useState<Proposal | undefined>(
    proposal,
  );

  const handleCommentsClick = useCallback(() => {
    commentsModalRef.current?.open();
  }, []);

  const changeStep = useCallback((stepNumber: ProposalStepType) => {
    setStep(stepNumber);
  }, []);

  const handleProposalUpdate = useCallback(
    (updatedProposal: Proposal) => {
      setCurrentProposal(updatedProposal);
      onProposalUpdate?.(updatedProposal);
    },
    [onProposalUpdate],
  );

  useEffect(() => {
    setCurrentProposal(proposal);
  }, [proposal]);

  const renderStepContent = useCallback(() => {
    const feedbackTargets =
      currentProposal?.targets?.filter(target =>
        target.targetTypes?.some(
          targetType => targetType.type === TargetTypeEnum.FEEDBACK,
        ),
      ) || [];

    switch (step) {
      case PROPOSAL_STEPS.PROPOSAL: // Proposal Step
        return (
          <ProposalStep
            targets={currentProposal?.targets || []}
            proposalUid={currentProposal?.uid ?? ''}
            proposalStatus={currentProposal?.status}
            hasManagerPermission
            hasEmployeePermission
            onProposalUpdate={handleProposalUpdate}
          />
        );

      case PROPOSAL_STEPS.FEEDBACK: // Feedback Step
        if (
          currentProposal?.status === ProposalStatusEnum.IN_PROGRESS_FEEDBACK
        ) {
          return (
            <FeedbackStep
              targets={currentProposal?.targets || []}
              proposalUid={currentProposal?.uid ?? ''}
              proposalStatus={currentProposal?.status}
              hasManagerPermission={false}
              hasEmployeePermission
              onProposalUpdate={handleProposalUpdate}
            />
          );
        } else {
          return (
            <Container className="flex flex-col gap-4">
              {feedbackTargets.length === 0 ? (
                <TabEmptyState
                  title="No feedback targets available"
                  description="There are no feedback targets to display at this time."
                />
              ) : (
                feedbackTargets.map(target => (
                  <TargetCard
                    key={target.uid}
                    data={target}
                    proposalStatus={currentProposal?.status}
                    currentTargetType={TargetTypeEnum.FEEDBACK}
                  />
                ))
              )}
            </Container>
          );
        }

      case PROPOSAL_STEPS.FINAL: // Final Step
        return (
          <FinalStep
            targets={currentProposal?.targets || []}
            proposalUid={currentProposal?.uid ?? ''}
            proposalStatus={currentProposal?.status}
            hasManagerPermission
            hasEmployeePermission={false}
            onProposalUpdate={handleProposalUpdate}
          />
        );

      case PROPOSAL_STEPS.COMPARATIVE: // Comparative Step
        if (currentProposal?.status === ProposalStatusEnum.COMPLETED) {
          return (
            <ComparativeStep
              targets={currentProposal?.targets || []}
              proposalStatus={currentProposal?.status}
            />
          );
        }
        return null;

      default:
        return null;
    }
  }, [step, currentProposal, handleProposalUpdate]);

  return (
    <ContentPage
      css={{
        '@lg': { pt: 0, px: '$md' },
      }}
      contentCss={{ bg: '$gray100', p: 0, gap: '$5' }}
      transparentContent
    >
      <Container className="flex flex-col gap-6 mb-20 h-full">
        <Container className="flex gap-2 items-center">
          <Button
            className="p-0"
            variant="tertiary"
            onClick={() => window.history.back()}
            iconLeft={<ChevronLeft />}
          >
            <Typography variant="body-sm-regular">Back</Typography>
          </Button>
        </Container>
        <Container className="flex gap-6 border-[1px] border-gray-300 rounded-lg bg-white items-center">
          <Container
            className='flex
            justify-between
            items-center
            border-0
            bg-no-repeat
            bg-[url("~/public/img/yellow-logo-partial.svg"),url("~/public/img/proposal-bg-employee.svg")]
            bg-[#191F2E]
            bg-[position:center_right,center_right]
            p-2.5
            px-6
            rounded-lg
            pr-24'
          >
            <Container>
              <IconWrapper variant={'primary'} round="md" size={32}>
                <Avatar
                  name={currentProposal?.employee?.name}
                  globalId={`${currentProposal?.employee?.globalId}`}
                />
              </IconWrapper>
              <Typography variant="title-sm-bold" className="ml-4 text-white">
                {`${currentProposal?.employee?.name}`}
              </Typography>
            </Container>
            {currentProposal?.status === PROPOSAL_STATUS.COMPLETED ? (
              <Badge className="p-[4px] h-[24px] bg-[#EBFCD5] text-[10px] text-[#44AC21] rounded-[8px] whitespace-nowrap flex items-center gap-1">
                <Check2 width={16} height={16} /> Completed
              </Badge>
            ) : currentProposal?.status?.startsWith(
                PROPOSAL_STATUS.IN_PROGRESS.toString(),
              ) ? (
              <Badge className="p-[4px] h-[24px] bg-[#FBF2B3] text-[10px] text-[#7D6F0B] rounded-[8px] whitespace-nowrap flex items-center gap-1">
                <InProgress size={16} /> In progress
              </Badge>
            ) : (
              <Badge className="p-[4px] h-[24px] bg-[#F5F6F7] text-[10px] text-[#7D8597] rounded-[8px] whitespace-nowrap flex items-center gap-1">
                <NotStarted /> Not Started
              </Badge>
            )}
          </Container>
          <Button
            variant="secondary"
            className="mr-8 border-[1px] border-gray-300 w-[300px]"
            iconLeft={<ChatSquareText />}
            onClick={handleCommentsClick}
          >
            Comments
          </Button>
        </Container>
        <Container className="flex gap-4 border-0 border-b border-gray-200">
          <Container
            className={`p-2 max-w-[80px] text-center cursor-pointer border-0 ${
              step === PROPOSAL_STEPS.PROPOSAL
                ? 'border-b-4 border-yellow-400'
                : ''
            }`}
            onClick={() => changeStep(PROPOSAL_STEPS.PROPOSAL)}
          >
            <Typography variant="body-sm-bold">Proposal</Typography>
          </Container>
          <Container
            className={`p-2 max-w-[80px] text-center cursor-pointer border-0 ${
              step === PROPOSAL_STEPS.FEEDBACK
                ? 'border-b-4 border-yellow-400'
                : ''
            }`}
            onClick={() => changeStep(PROPOSAL_STEPS.FEEDBACK)}
          >
            <Typography variant="body-sm-bold">Feedback</Typography>
          </Container>
          <Container
            className={`p-2 max-w-[80px] text-center cursor-pointer border-0 ${
              step === PROPOSAL_STEPS.FINAL
                ? 'border-b-4 border-yellow-400'
                : ''
            }`}
            onClick={() => changeStep(PROPOSAL_STEPS.FINAL)}
          >
            <Typography variant="body-sm-bold">Final</Typography>
          </Container>
          {currentProposal?.status === ProposalStatusEnum.COMPLETED && (
            <Container
              className={`p-2 max-w-[100px] text-center cursor-pointer border-0 ${
                step === PROPOSAL_STEPS.COMPARATIVE
                  ? 'border-b-4 border-yellow-400'
                  : ''
              }`}
              onClick={() => changeStep(PROPOSAL_STEPS.COMPARATIVE)}
            >
              <Typography variant="body-sm-bold">Comparative</Typography>
            </Container>
          )}
        </Container>

        {step === PROPOSAL_STEPS.PROPOSAL && (
          <Container className="h-full">{renderStepContent()}</Container>
        )}
        {step === PROPOSAL_STEPS.FEEDBACK && (
          <Container className="h-full">{renderStepContent()}</Container>
        )}
        {step === PROPOSAL_STEPS.FINAL && (
          <Container className="h-full">{renderStepContent()}</Container>
        )}
        {step === PROPOSAL_STEPS.COMPARATIVE &&
          currentProposal?.status === ProposalStatusEnum.COMPLETED && (
            <Container className="h-full">{renderStepContent()}</Container>
          )}
      </Container>

      <CommentsModal
        ref={commentsModalRef}
        parentId={currentProposal?.uid || ''}
      />
    </ContentPage>
  );
}
