import {
  Button,
  Container,
  Drawer,
  Separator,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';
import { Form, Formik } from 'formik';

import { Proposal } from '~/shared/types/Proposal';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { ThemeFormFields } from './ThemeFormFields';
import { CreateThemeFormValues, useCreateTheme } from '../hooks/useCreateTheme';

type CreateThemeFormValuesLocal = CreateThemeFormValues;

type CreateThemeDrawerProps = {
  proposalId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccessSubmit: (response: Proposal) => void;
  targetType: TargetTypeEnum;
};

export function CreateThemeDrawer({
  proposalId,
  isOpen,
  onClose,
  onSuccessSubmit,
  targetType,
}: CreateThemeDrawerProps) {
  const { t } = useTranslate();
  const { isLoading, mutate } = useCreateTheme(
    proposalId,
    onSuccessSubmit,
    onClose,
    targetType,
  );

  const initialValues: CreateThemeFormValuesLocal = {
    name: '',
    calculationMethod: '',
    definition: '',
    scope: '',
    weight: 0,
  };

  return (
    <Drawer.Root direction="right" open={isOpen} onOpenChange={onClose}>
      <Drawer.Content className="w-full max-w-lg pt-12 h-[calc(100%-74px)]">
        <Formik<CreateThemeFormValuesLocal>
          initialValues={initialValues}
          validate={values => {
            const errors: Partial<
              Record<keyof CreateThemeFormValuesLocal, string>
            > = {};
            if (!values.name || values.name.trim() === '') {
              errors.name = t('common_required', {
                field: t('common_name'),
              });
            }
            if (
              !values.calculationMethod ||
              values.calculationMethod.trim() === ''
            ) {
              errors.calculationMethod = t('common_required', {
                field: t('common_calculation_method_deliverables'),
              });
            }
            return errors;
          }}
          onSubmit={async values => {
            mutate(values);
          }}
        >
          {({ submitForm, handleChange, values }) => (
            <>
              <Container className="overflow-y-auto h-full">
                <Container className="flex justify-between items-center p-4">
                  <Container className="flex flex-col">
                    <Typography variant="body-sm-bold">
                      {t('create_theme')}
                    </Typography>
                  </Container>
                </Container>
                <Separator />
                <ThemeFormFields values={values} onChange={handleChange} />
              </Container>
              <Separator />
              <Container className="flex flex-row justify-end p-4 gap-4 w-full">
                <Button
                  variant="secondary"
                  border="default"
                  disabled={isLoading}
                  onClick={onClose}
                >
                  {t('common_cancel')}
                </Button>
                <Button
                  variant="primary"
                  disabled={isLoading}
                  isLoading={isLoading}
                  onClick={submitForm}
                >
                  {t('common_save')}
                </Button>
              </Container>
            </>
          )}
        </Formik>
      </Drawer.Content>
    </Drawer.Root>
  );
}
