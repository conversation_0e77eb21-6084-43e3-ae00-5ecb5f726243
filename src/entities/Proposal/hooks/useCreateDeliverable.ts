import { useMutation } from '@tanstack/react-query';

import deliverablesService from '~/shared/services/deliverables';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { DeliverableTypeEnum } from '~/shared/types/DeliverableType';

export type CreateDeliverableInput = {
  name: string;
  calculationMethod: string;
  definition: string;
  isActive?: boolean;
  type?: DeliverableTypeEnum;
};

export function useCreateDeliverable() {
  const { mutateAsync } = useMutation({
    mutationFn: (input: CreateDeliverableInput): Promise<DeliverableItem> => {
      const body = {
        isActive: true,
        type: input.type ?? DeliverableTypeEnum.DRAFT,
        ...input,
      };
      return deliverablesService.createDeliverable(body);
    },
  });

  return {
    createDeliverable: mutateAsync,
  };
}
