import { useMutation } from '@tanstack/react-query';

import proposalService from '~/shared/services/proposal';
import { DeliverableTypeEnum } from '~/shared/types/DeliverableType';
import { Proposal } from '~/shared/types/Proposal';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { useCreateDeliverable } from './useCreateDeliverable';

export type CreateThemeFormValues = {
  name: string;
  calculationMethod: string;
  definition: string;
  scope: string;
  weight: number;
};

export function useCreateTheme(
  proposalId: string,
  onSuccessSubmit: (proposal: Proposal) => void,
  onClose: () => void,
  targetType: TargetTypeEnum,
) {
  const { createDeliverable } = useCreateDeliverable();
  const { isLoading, mutate } = useMutation({
    mutationFn: async (values: CreateThemeFormValues) => {
      const created = await createDeliverable({
        name: values.name,
        calculationMethod: values.calculationMethod,
        definition: values.definition,
        type: DeliverableTypeEnum.DRAFT,
      });

      return proposalService.createTarget(proposalId, {
        targets: [
          {
            weight: Number(values.weight) || 0,
            scope: values.scope,
            targetType,
            uidDeliverable: created.uid,
          },
        ],
      });
    },
    onSuccess: (proposal: Proposal) => {
      onSuccessSubmit(proposal);
      onClose();
    },
    onError: () => {},
  });

  return { isLoading, mutate };
}
