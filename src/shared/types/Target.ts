import { TargetTypeEnum } from '../utils/enums/target-type';

import { DeliverableItem } from './Deliverable';
import { Proposal } from './Proposal';
import { TargetType } from './TargetType';

export type Target = {
  uid?: string;
  weight?: number;
  scope?: string;
  uidDeliverable?: string;
  uidParentTarget?: string;
  uidProposal?: string;
  proposal?: Proposal;
  deliverable?: DeliverableItem;
  parentTarget?: Target;
  children?: Target[];
  targetTypes?: TargetType[];
  targetType?: TargetTypeEnum;
};
