import { DeliverableItem } from '~/shared/types/Deliverable';
import { DeliverableTypeEnum } from '~/shared/types/DeliverableType';
import { Owners } from '~/shared/types/Owners';

export type CatalogItemDetailCardContent =
  | string
  | string[]
  | number
  | Owners[]
  | undefined
  | DeliverableItem[];

export type CatalogItemDetailCardProps = {
  info: string;
  content?: CatalogItemDetailCardContent;
  onCopy: () => void;
  icon?: React.ReactNode;
  className?: string;
  isActive?: boolean;
  type?: DeliverableTypeEnum;
};
