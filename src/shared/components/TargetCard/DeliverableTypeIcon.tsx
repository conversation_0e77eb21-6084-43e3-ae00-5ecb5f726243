import { BarChartFill, ListCheck } from 'react-bootstrap-icons';
import { IconWrapper } from '@ghq-abi/design-system-v2';

import { DeliverableTypeEnum } from '~/shared/types/DeliverableType';
import { Target } from '~/shared/types/Target';

type DeliverableTypeIconProps = {
  target: Target;
};

function isTargetFromType(target: Target, type: DeliverableTypeEnum) {
  return (
    target.deliverable?.type === type ||
    target.deliverable?.deliverableType?.code === type
  );
}

export function DeliverableTypeIcon({ target }: DeliverableTypeIconProps) {
  const child = target.children?.[0] ? target.children[0] : target;
  const isKPI = isTargetFromType(child, DeliverableTypeEnum.KPI);

  function getIcon() {
    return isKPI ? <BarChartFill size={24} /> : <ListCheck size={24} />;
  }

  function getVariant() {
    const isDraft = isTargetFromType(child, DeliverableTypeEnum.DRAFT);
    if (isDraft) {
      return 'disabled-2';
    }

    return isKPI ? 'secondary' : 'primary';
  }

  return (
    <IconWrapper variant={getVariant()} round="md" size={42}>
      {getIcon()}
    </IconWrapper>
  );
}
